from enum import Enum
import json
import traceback
import asyncio
from fastapi import HTTPException
from typing import Any, Dict, List
from uuid import uuid4
from datetime import datetime, timezone

from agent_server.utils.redis_util import redis_client
from agent_server.core.message.transmitter import Transmitter
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.conversation import conversation_curd
from agent_server.core.services.database.schemas.conversation import (
    ConversationCreate,
    ConversationTable,
)
from agent_server.core.message.types import MessageType, MessagePackage
from agent_server.core.services.database.schemas.message import MessageCreate
from agent_server.core.services.database.crud.message import message_curd
from agent_server.core.config.app_config import config


class MessageSignal(str, Enum):
    Error = "[[[ERROR]]]"
    Start = "[[[START]]]"
    Done = "[[[DONE]]]"
    Cancel = "[[[CANCEL]]]"


def create_error_message(error: str):
    return f"{MessageSignal}: {error}"

def create_start_message():
    return MessageSignal.Start

def create_done_message():
    return MessageSignal.Done

def create_cancel_message(reason: str):
    return f"{MessageSignal.Cancel}: {reason}"

def push_message_chunk(task_id: str, message: str):
    redis_client.client.lpush(task_id, message)

def get_message_content(message: str):
    return message[13:]

def getPreservedKey(key: str):
    return f"{key}_preserved"

async def message_generate(task_id: str, message_generator: Any):
    try:
        async for msg in message_generator:
            # 收到 cancel 信号中断生成
            head = redis_client.client.lindex(task_id, 0)
            if head and head.startswith(MessageSignal.Cancel):
                return
            push_message_chunk(task_id=task_id, message=msg)
    except Exception as e:
        print(f"Error: {e}")
        push_message_chunk(task_id=task_id, message=create_error_message(str(e) + "\n"))
    finally:
        push_message_chunk(task_id=task_id, message=create_done_message())


async def run_generate_message_task(
    task_id: str,
    conversation_id: str,
    agent_code: str,
    message_generator_fn: Any,
):
    """Run task in thread with synchronous database operations"""
    push_message_chunk(task_id=task_id, message=create_start_message())
    # 消息任务有效期24小时
    redis_client.client.expire(task_id, config.message.task_expired)

    async def task():
        try:
            transmitter = Transmitter(
                conversation_id=conversation_id,
                message_id=uuid4().hex,
                agent_code=agent_code,
            )

            await message_generate(
                task_id,
                message_generator_fn(transmitter),
            )

        except Exception as e:
            print(f"Task error: {e}")
            push_message_chunk(task_id=task_id, message=create_error_message(e))

    # 后台运行 task
    generate_task = asyncio.create_task(task())
    asyncio.shield(generate_task)

    # Run in a separate thread
    # thread = threading.Thread(target=run_task)
    # thread.daemon = True  # Thread will die when main program exits
    # thread.start()


def subscribe_message(task_id):
    result = redis_client.client.brpop(task_id, config.message.waiting_timeout)

    if result and result[1]:
        preserved_data_key = getPreservedKey(task_id)
        existed_preserved = redis_client.client.exists(preserved_data_key)
        redis_client.client.lpush(preserved_data_key, result[1])
        if not existed_preserved:
            # 消息任务有效期24小时
            redis_client.client.expire(preserved_data_key, config.message.task_expired)

        return result[1]
    else:
        return None


async def content_stream_generator(task_id: str, conversation_id: str):
    # TODO: 优化异步问题，等待任务更新到 redis
    await asyncio.sleep(0.5)
    try:
        if redis_client.client.exists(task_id):
            preserved_data_key = getPreservedKey(task_id)
            all_created_messages = redis_client.client.lrange(preserved_data_key, 0, -1)
            if all_created_messages:
                all_created_messages.reverse()
                for message in all_created_messages:
                    if message == MessageSignal.Start:
                        continue
                    if message == MessageSignal.Done:
                        return
                    if message.startswith(MessageSignal.Cancel):
                        return
                    yield message

            while True:
                message = await asyncio.to_thread(subscribe_message, task_id)
                if not message:
                    return
                if message == MessageSignal.Start:
                    continue
                if message == MessageSignal.Done:
                    return
                if message.startswith(MessageSignal.Cancel):
                    return
                if message.startswith(MessageSignal.Error):
                    raise Exception(get_message_content(message))
                else:
                    yield message
        else:
            # 任务异常，移除任务，提示重试
            if conversation_id:
                async with db_manager.session() as session:
                    conversation_list = await conversation_curd.get_by_conversation_id(
                        session, _id=conversation_id
                    )
                    if conversation_list:
                        await conversation_curd.update(
                            db=session,
                            db_obj=conversation_list[0],
                            obj_input={"task_id": None},
                        )
            # raise HTTPException(status_code=404, detail="消息任务异常")
            yield "[DONE]"
    except HTTPException:
        yield ""
    except Exception as e:
        traceback.print_exc()

        # 返回给前端的错误信息包含：
        error_info = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "stack_trace": traceback.format_exc(),
        }
        yield json.dumps({
            "code": 500,
            "message": f"{type(e).__name__} - {str(e)}",
            "error": error_info,
        })


async def create_update_conversation(
    message: str,
    conversation: ConversationTable | None,
    agent_code: str,
    user_id: int | str,
):
    task_id = f"message-task:{uuid4().hex}"
    # 新建会话，更新task_id
    conversation_id = None
    if conversation:
        conversation_id = conversation.id

    async with db_manager.session() as session:
        if not conversation:
            title = message[:20]
            new_conversation = ConversationCreate(
                user_id=user_id,
                title=title,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                current_agent_code=agent_code,
                task_id=task_id,
            )
            new_conversation = await conversation_curd.create(
                db=session,
                obj_input=new_conversation,
            )
            conversation_id = new_conversation.id
        else:
            if conversation:
                await conversation_curd.update(
                    db=session,
                    db_obj=conversation,
                    obj_input={"task_id": task_id},
                )

        return conversation_id, task_id


async def save_user_message(
    *,
    message: str,
    agent_code: str,
    conversation_id: str,
    data: Dict[str, Any],
    hidden=False,
    files=List[str]
):
    # 保存用户消息
    message_pkg = MessagePackage(
        package_id=0,
        package_type=0,
        status=1,
        data=message,
    )
    async with db_manager.session() as session:
        new_message = MessageCreate(
            agent_code=agent_code,
            conversation_id=conversation_id,
            message_type=MessageType.HUMAN,
            content=[message_pkg.model_dump()],
            data_object=data,
            hidden=hidden,
            files=files,
        )
        await message_curd.create(db=session, obj_input=new_message)
