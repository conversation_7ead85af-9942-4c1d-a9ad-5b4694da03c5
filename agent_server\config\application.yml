# 模型配置
ai:
  models:
    default: volcengine  # 默认模型标识符
    qwen:
      name: qwen-max
      api_key: your-qwen-api-key
      base_url: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation

    ollama:
      name: llama3
      api_key: dummy-key
      base_url: http://localhost:11434/v1

    custom:
      name: aliyun/qwen-max
      api_key: sk-cjey9DW7GA2DY4idYdWtMVXPLHHLKuKtP1rcrlUX38QzBTrH
      base_url: http://***********:3000/v1

    volcengine:
      name: volcengine/deepseek-v3
      api_key: sk-giZY3EQGWzUhpuyslUfl5KWIv7wPh0nFul0k56wems7NnTXy
      base_url: http://***********:3000/v1  

    csci:
      name: csci/qwen3-235b
      api_key: sk-d66gLVfcKT3jiIOW2CCINGLQZFMzuSrsJ5lX69E7kogTmvjM
      base_url: http://***********:3000/v1

    elephant:
      name: deepseek-chat
      api_key: ***********************************
      base_url: https://api.deepseek.com/v1 

# 认证配置 adapter:实现类 agent_server.core.auth.default_auth_adapter.DefaultAuthAdapter  auth_url:用户认证接口
auth:
  adapter: agent_server.core.auth.default_auth_adapter.DefaultAuthAdapter
  auth_url: http://************:8002/api/user/basic/current


# 获取预览图片路径配置
commonPictureUrl: http://************:8002/api/common/getPictureUrls

  
# 大B端配置
business_system:
  host: ************
  port: 8002

# Redis 配置
redis:
  host: ************
  port: 6389
  password: elephantredis
  db: 0

# PostgreSQL 配置
database:
  url: postgresql+asyncpg://postgres:postgres@************:5432/agent_db

# Agent 配置
agents:
#  - name: dynamic-page-creator
#    impl: "agent_server.core.api.dynamic_creator_sender.PageCreatorSender"
#    exclude_tools:  # 排除的工具列表
#      - call_nl2sql_function
#      - query_company_info
#
#  - name: dynamic-page-qa
#    impl: "agent_server.core.api.dynamic_qa_sender.PageQaSender"
#
#  - name: nl2sql-agent
#    impl: "agent_server.core.api.nl2sql_sender.NL2SqlSender"
#
#  - name: elephant-agent
#    impl: "agent_server.core.api.elephant_sender.ElephantSender"
#    checkpoint: memory

  - name: test
    impl: "agent_server.playground.test.sender.TestAgent"


#mcp_servers:
#  - key: dynamic-page-server
#    type: stdio
#    url: agent_server/mcpserver/mcp_server.py
#    enabled: true
#
#  - key: other-server
#    type: sse
#    url: http://************:5602/sse/1
#    enabled: false
    
gateway:
  services:
    - name: cluster
      url: http://************:8002/api

    - name: nl2sql
      url: http://***********:8000/api

# 默认提示词模版位置
prompt_dir: ./prompts

# 消息任务配置
message:
  task_expired: 86400  # 任务过期时间(秒) - 24小时 (60 * 60 * 24)
  waiting_timeout: 60  # 等待超时时间(秒)

# 后台管理系统配置
admin:
  # 管理员账户配置
  users:
    admin:
      # 密码: admin123 (bcrypt加密)
      password: "$2b$12$w76kENKnxdT5nCEZe7Yneezn358is/FRcdfD3yhGZOBvo4GCq358u"
      role: "super_admin"
    manager:
      # 密码: manager123 (bcrypt加密)
      password: "$2b$12$qDcOjbYiMZ.FO6bBJVeNA.Web/dbTMVN3axfgZVFq4DB4zOu0Q2Q2"
      role: "admin"
  # 系统配置
  system:
    title: "Agent Server 管理后台"
    session_timeout: 3600  # 会话超时时间(秒)
    port: 8001  # 管理后台端口
    storage_secret: "4eecbc7b84ec7d094f008116e78e8bb9fe3ffdab9ffd62adf32352eafd0639c2"  # 会话存储加密密钥


# 路由注册配置
routers:
  # 自动发现配置
  auto_discovery:
    enabled: true  # 是否启用自动发现
    packages:  # 要扫描的包列表
      - "span.api"
      - "plugins.api"
      - "rags.api"

  # 手动注册的路由（优先级高于自动发现）
  manual:
    - module_path: "agent_rags.rags.api.knowledge_base_api"
      router_name: "router"
      prefix: "/rags"
      tags: ["RAGs知识库"]
      enabled: false
      description: "RAGs知识库管理API"
      priority: 100


# rag 服务配置
rag_service:
  services:
    - name: ragflow
      base_url: "http://***********:8080"
      api_key: "ragflow-U2MTMzZmFjOGEwNzExZjBhMjA2NmFhND"
      dataset_id: "7f74e7128d5611f094736aa462ef87c6" 
      timeout: 120
      chunk_method: "naive"
      module_path: "agent_server.core.rag.ragflow"

rags:
  # 是否启用RAGs模块
  enabled: true
  # 向量数据库配置
  vector_store:
    type: "milvus"  # 支持: milvus, chroma, faiss
    milvus:
      uri: "http://************:19530"
      user: ""
      password: ""
      db_name: "rags_db"
      collection_prefix: "kb_"  # 知识库集合前缀

  # 嵌入模型配置
  embedding:
    base_url: "http://***********:3000/v1"
    api_key: "sk-yXAZewxvouZTYAZI4COXbJWl2Zc06sZWrXkwJcqArUTvNOSu"
    model: "volcengine/doubao-embedding"
    max_tokens: 8192
    batch_size: 100  # 批处理大小

  # 重排序模型配置
  rerank:
    base_url: "http://***********:3000/v1"
    api_key: "sk-SewFdoWL9JQSr6Vcb1gwBdHlRv18ZXZdxOg1AEBJwdJ9xSdm"
    model: "aliyun/gte-rerank-v2"
    max_tokens: 4096

  # 文档分段配置
  text_splitter:
    chunk_size: 1024  # 分段最大长度
    chunk_overlap: 51  # 分段重叠长度
    separators: ["\n\n"]  # 分段标识符

  # 检索配置
  retrieval:
    vector_search:
      top_k: 10  # 向量检索返回数量
      score_threshold: 0.7  # 相似度阈值
    fulltext_search:
      top_k: 10  # 全文检索返回数量
      score_threshold: 0.5  # 相关性阈值
    hybrid_search:
      top_k: 20  # 混合检索初始返回数量
      final_top_k: 10  # 重排序后最终返回数量
      vector_weight: 0.7  # 向量检索权重
      fulltext_weight: 0.3  # 全文检索权重
      score_threshold: 0.6  # 最终相似度阈值
