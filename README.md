# agent-server

LLM基础后端服务

## 开发

### 安装依赖
```sh
pip install uv

uv sync
```

### 启动服务

#### 🚀 统一启动（推荐）

**使用 UV（推荐）:**
```sh
# 默认启动 - 自动检测并启动所有可用服务
uv run python main.py

# 或使用专用的UV启动脚本（包含环境检查）
uv run python start_uv.py
```

**使用标准 Python:**
```sh
python main.py
```

#### 📋 启动选项

**UV 环境:**
```sh
# 仅启动 API 服务器（禁用管理后台）
uv run python main.py --no-admin

# 仅启动管理后台
uv run python main.py --admin-only

# 调试模式（支持热重载）
uv run python main.py --debug

# 自定义端口
uv run python main.py --port 9000
```

**标准 Python:**
```sh
# 仅启动 API 服务器（禁用管理后台）
python main.py --no-admin

# 仅启动管理后台
python main.py --admin-only

# 调试模式（支持热重载）
python main.py --debug

# 自定义端口
python main.py --port 9000

# 查看所有选项
python main.py --help
```

#### 🎛️ 服务访问地址
- **API 服务器**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **管理后台**: http://localhost:8001/admin
- **默认登录**: admin / admin123

> 💡 **提示**: 管理后台需要在 `config/application.yml` 中配置 `admin` 部分才能启用

## docker 部署

### 本地构建
```bash
# develop 版本
# 构建镜像 PowerShell
# $env:HTTPS_PROXY="http://127.0.0.1:10888"
docker build -t agent-server/latest .

# 保存镜像 WSL 环境
docker save agent-server/latest | gzip > ./docker/agent_server_latest.tar.gz


# prod 版本
docker build -t agent-server/prod .
docker save agent-server/prod | gzip > ./docker/agent_server_prod.tar.gz
```

### 服务器部署
```bash
# 解压镜像
gunzip -c agent_server_latest.tar.gz > agent_server_latest.tar

# 删除旧的镜像
docker rmi agent-server/latest

# 加载镜像
docker load < agent_server_latest.tar

# 删除旧容器
sudo docker container stop agent-server
sudo docker container rm agent-server

# 运行容器
sudo docker run \
  --add-host=host.docker.internal:host-gateway \
  -e APP_ENV="prod" \
  -e CONFIG_PATH="/app/config/application.yml" \
  -v /data/agent/app/logs:/app/logs \
  -v /data/agent/app/config:/app/config \
  -p 5801:8000 \
  --name agent-server \
  -d agent-server/latest
```

## 数据库

### 备份

```bash
sudo docker exec -t timescaledb pg_dump -U postgres -Fc agent_db > /data/backups/my_database_$(date +%Y%m%d%H%M%S).dump
```

## Todo List

- [x] 配置文件移到镜像外，通过环境变量配置路径
- [x] log 文件目录
- [ ] 多 Agent 集成架构设计
- [ ] 通用异常判断
- [ ] 通用 Response
- [ ] 打包配置
- [ ] run_generate_message_task 改成 asyncio.run_task 执行，去掉新建 Threat

## 运行 框架开发人员
uv pip install -e .[dev]
python -m agent_system.system.main
python setup.py bdist_wheel

## 查看
http://gitlab.chinacsci.com/api/v4/projects/3593/packages/pypi/simple/agent-server/

## 安装
pip install agent-server --index-url http://__token__:<your-token>@gitlab.chinacsci.com/api/v4/projects/3593/packages/pypi/simple --trusted-host gitlab.chinacsci.com
